import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:my_115_app/pages/token_manager_page.dart';
import 'package:my_115_app/services/talker_service.dart';

void main() {
  // 设置测试环境
  setUpAll(() {
    // 注册测试用的TalkerService
    if (!GetIt.instance.isRegistered<TalkerService>()) {
      GetIt.instance.registerSingleton<TalkerService>(TalkerService());
    }
  });

  tearDownAll(() {
    // 清理测试环境
    GetIt.instance.reset();
  });

  group('TokenManagerPage Tests', () {
    testWidgets('TokenManagerPage should build and show basic structure', (WidgetTester tester) async {
      // 创建一个简单的测试应用
      await tester.pumpWidget(
        MaterialApp(
          home: const TokenManagerPage(),
        ),
      );

      // 等待初始化完成
      await tester.pump();

      // 验证页面能够构建（即使可能显示加载状态）
      expect(find.byType(TokenManagerPage), findsOneWidget);
      expect(find.byType(Scaffold), findsOneWidget);
    });

    testWidgets('Should show app bar with correct title', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const TokenManagerPage(),
        ),
      );

      // 等待初始化
      await tester.pump();

      // 验证AppBar和标题
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.text('Token 管理'), findsOneWidget);
    });

    testWidgets('Should handle initialization gracefully', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const TokenManagerPage(),
        ),
      );

      // 等待所有异步操作完成
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // 验证页面没有崩溃
      expect(find.byType(TokenManagerPage), findsOneWidget);
    });

  });

  group('TokenManagerPage Basic Widget Tests', () {
    testWidgets('Should contain basic UI components', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const TokenManagerPage(),
        ),
      );

      // 等待初始化
      await tester.pump();

      // 验证基本组件存在
      expect(find.byType(Scaffold), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
    });
  });
}
